package com.sbg.ug.optimus_backend.billing.orchastration;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class TransactionContext implements Serializable {
    private static final long serialVersionUID = 1L;
    private final Map<String, Object> data = new ConcurrentHashMap<>();
    private final List<String> executedSteps = new ArrayList<>();

    public void put(String key, Object value) {
        data.put(key, value);
    }

    @SuppressWarnings("unchecked")
    public <T> T get(String key) {
        return (T) data.get(key);
    }

    public void addExecutedStep(String stepName) {
        executedSteps.add(stepName);
    }

    public List<String> getExecutedSteps() {
        return new ArrayList<>(executedSteps);
    }
}
