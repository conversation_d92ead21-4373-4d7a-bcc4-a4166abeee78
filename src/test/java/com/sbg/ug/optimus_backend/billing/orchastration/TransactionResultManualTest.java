package com.sbg.ug.optimus_backend.billing.orchastration;

/**
 * Manual test to demonstrate TransactionResult defensive copying behavior.
 * This is not a JUnit test but a simple demonstration class.
 */
public class TransactionResultManualTest {

    public static void main(String[] args) {
        System.out.println("=== TransactionResult Defensive Copying Demo ===\n");

        // Create test data
        TransactionContext originalContext = new TransactionContext();
        originalContext.put("user_id", "12345");
        originalContext.put("amount", 100.50);
        originalContext.addExecutedStep("validate_user");
        originalContext.addExecutedStep("check_balance");

        RuntimeException originalException = new RuntimeException("Payment failed");

        // Create TransactionResult
        TransactionResult result = TransactionResult.failure(originalException, originalContext);

        System.out.println("1. Original Context Data:");
        System.out.println("   - user_id: " + originalContext.get("user_id"));
        System.out.println("   - amount: " + originalContext.get("amount"));
        System.out.println("   - executed steps: " + originalContext.getExecutedSteps());
        System.out.println("   - context hash: " + originalContext.hashCode());

        System.out.println("\n2. Original Exception:");
        System.out.println("   - message: " + originalException.getMessage());
        System.out.println("   - exception hash: " + originalException.hashCode());

        // Get defensive copies
        TransactionContext copiedContext = result.context();
        Exception copiedException = result.exception();

        System.out.println("\n3. Copied Context Data:");
        System.out.println("   - user_id: " + copiedContext.get("user_id"));
        System.out.println("   - amount: " + copiedContext.get("amount"));
        System.out.println("   - executed steps: " + copiedContext.getExecutedSteps());
        System.out.println("   - context hash: " + copiedContext.hashCode());
        System.out.println("   - same instance? " + (originalContext == copiedContext));

        System.out.println("\n4. Copied Exception:");
        System.out.println("   - message: " + copiedException.getMessage());
        System.out.println("   - exception hash: " + copiedException.hashCode());
        System.out.println("   - same instance? " + (originalException == copiedException));

        // Modify the copies
        copiedContext.put("user_id", "MODIFIED");
        copiedContext.put("new_field", "added_field");
        copiedContext.addExecutedStep("modified_step");

        System.out.println("\n5. After Modifying Copied Context:");
        System.out.println("   Original context user_id: " + originalContext.get("user_id"));
        System.out.println("   Copied context user_id: " + copiedContext.get("user_id"));
        System.out.println("   Original context new_field: " + originalContext.get("new_field"));
        System.out.println("   Copied context new_field: " + copiedContext.get("new_field"));
        System.out.println("   Original executed steps count: " + originalContext.getExecutedSteps().size());
        System.out.println("   Copied executed steps count: " + copiedContext.getExecutedSteps().size());

        // Test multiple calls return new copies each time
        TransactionContext copy1 = result.context();
        TransactionContext copy2 = result.context();
        Exception exception1 = result.exception();
        Exception exception2 = result.exception();

        System.out.println("\n6. Multiple Calls Return New Copies:");
        System.out.println("   Context copy1 == copy2? " + (copy1 == copy2));
        System.out.println("   Exception copy1 == copy2? " + (exception1 == exception2));
        System.out.println("   Context copy1 hash: " + copy1.hashCode());
        System.out.println("   Context copy2 hash: " + copy2.hashCode());

        // Test success case
        TransactionResult successResult = TransactionResult.success(originalContext);
        System.out.println("\n7. Success Case:");
        System.out.println("   Success: " + successResult.success());
        System.out.println("   Exception: " + successResult.exception());
        System.out.println("   Context same instance? " + (originalContext == successResult.context()));

        System.out.println("\n=== Demo Complete ===");
    }
}
