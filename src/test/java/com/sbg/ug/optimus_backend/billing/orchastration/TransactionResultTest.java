package com.sbg.ug.optimus_backend.billing.orchastration;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

@DisplayName("TransactionResult Tests")
class TransactionResultTest {

    private TransactionContext testContext;
    private Exception testException;

    @BeforeEach
    void setUp() {
        testContext = new TransactionContext();
        testContext.put("test_key", "test_value");
        testContext.addExecutedStep("step1");

        testException = new IllegalArgumentException("Test exception message");
    }

    @Nested
    @DisplayName("Factory Methods")
    class FactoryMethodsTest {

        @Test
        @DisplayName("success() should create successful TransactionResult")
        void success_shouldCreateSuccessfulResult() {
            // Act
            TransactionResult result = TransactionResult.success(testContext);

            // Assert
            assertTrue(result.success(), "Result should be successful");
            assertNull(result.exception(), "Exception should be null for successful result");
            assertNotNull(result.context(), "Context should not be null");
            // Verify context data is preserved
            assertEquals("test_value", result.context().get("test_key"));
            assertEquals(1, result.context().getExecutedSteps().size());
            assertEquals("step1", result.context().getExecutedSteps().get(0));
        }

        @Test
        @DisplayName("success() should handle null context")
        void success_shouldHandleNullContext() {
            // Act
            TransactionResult result = TransactionResult.success(null);

            // Assert
            assertTrue(result.success(), "Result should be successful");
            assertNull(result.exception(), "Exception should be null");
            assertNull(result.context(), "Context should be null");
        }

        @Test
        @DisplayName("failure() should create failed TransactionResult")
        void failure_shouldCreateFailedResult() {
            // Act
            TransactionResult result = TransactionResult.failure(testException, testContext);

            // Assert
            assertFalse(result.success(), "Result should not be successful");
            assertNotNull(result.exception(), "Exception should not be null");
            assertNotNull(result.context(), "Context should not be null");
            // Verify exception and context data are preserved
            assertEquals("Test exception message", result.exception().getMessage());
            assertEquals("test_value", result.context().get("test_key"));
        }

        @Test
        @DisplayName("failure() should handle null exception")
        void failure_shouldHandleNullException() {
            // Act
            TransactionResult result = TransactionResult.failure(null, testContext);

            // Assert
            assertFalse(result.success(), "Result should not be successful");
            assertNull(result.exception(), "Exception should be null");
            assertNotNull(result.context(), "Context should not be null");
        }

        @Test
        @DisplayName("failure() should handle null context")
        void failure_shouldHandleNullContext() {
            // Act
            TransactionResult result = TransactionResult.failure(testException, null);

            // Assert
            assertFalse(result.success(), "Result should not be successful");
            assertNotNull(result.exception(), "Exception should not be null");
            assertNull(result.context(), "Context should be null");
        }

        @Test
        @DisplayName("failure() should handle both null exception and context")
        void failure_shouldHandleBothNull() {
            // Act
            TransactionResult result = TransactionResult.failure(null, null);

            // Assert
            assertFalse(result.success(), "Result should not be successful");
            assertNull(result.exception(), "Exception should be null");
            assertNull(result.context(), "Context should be null");
        }
    }

    @Nested
    @DisplayName("Exception Defensive Copying")
    class ExceptionDefensiveCopyingTest {

        @Test
        @DisplayName("exception() should return defensive copy using SerializationUtils.clone")
        void exception_shouldReturnDefensiveCopy() {
            // Arrange
            TransactionResult result = TransactionResult.failure(testException, testContext);

            // Act
            Exception returnedException = result.exception();

            // Assert
            assertNotNull(returnedException, "Returned exception should not be null");
            assertNotSame(
                    testException,
                    returnedException,
                    "Should return a defensive copy, not the same instance");
            assertEquals(
                    testException.getMessage(),
                    returnedException.getMessage(),
                    "Exception message should be preserved");
            assertEquals(
                    testException.getClass(),
                    returnedException.getClass(),
                    "Exception type should be preserved");
        }

        @Test
        @DisplayName("exception() should return null when original exception is null")
        void exception_shouldReturnNullWhenOriginalIsNull() {
            // Arrange
            TransactionResult result = TransactionResult.failure(null, testContext);

            // Act
            Exception returnedException = result.exception();

            // Assert
            assertNull(returnedException, "Should return null when original exception is null");
        }

        @Test
        @DisplayName("exception() defensive copy should not affect original when modified")
        void exception_defensiveCopyShouldNotAffectOriginal() {
            // Arrange
            RuntimeException originalException = new RuntimeException("Original message");
            TransactionResult result = TransactionResult.failure(originalException, testContext);

            // Act
            Exception returnedException = result.exception();
            // Modify the returned exception (if it has mutable state)
            returnedException.addSuppressed(new RuntimeException("Suppressed exception"));

            // Assert
            assertNotSame(originalException, returnedException, "Should be different instances");
            assertEquals(
                    0,
                    originalException.getSuppressed().length,
                    "Original exception should not have suppressed exceptions");
            assertEquals(
                    1,
                    returnedException.getSuppressed().length,
                    "Returned exception should have suppressed exception");
        }
    }

    @Nested
    @DisplayName("Context Defensive Copying")
    class ContextDefensiveCopyingTest {

        @Test
        @DisplayName("context() should return defensive copy using SerializationUtils.clone")
        void context_shouldReturnDefensiveCopy() {
            // Arrange
            TransactionResult result = TransactionResult.success(testContext);

            // Act
            TransactionContext returnedContext = result.context();

            // Assert
            assertNotNull(returnedContext, "Returned context should not be null");
            assertNotSame(
                    testContext,
                    returnedContext,
                    "Should return a defensive copy, not the same instance");
            // Verify the data is preserved in the copy
            assertEquals("test_value", returnedContext.get("test_key"));
            assertEquals(1, returnedContext.getExecutedSteps().size());
            assertEquals("step1", returnedContext.getExecutedSteps().get(0));
        }

        @Test
        @DisplayName("context() should return null when original context is null")
        void context_shouldReturnNullWhenOriginalIsNull() {
            // Arrange
            TransactionResult result = TransactionResult.success(null);

            // Act
            TransactionContext returnedContext = result.context();

            // Assert
            assertNull(returnedContext, "Should return null when original context is null");
        }

        @Test
        @DisplayName("context() defensive copy should not affect original when modified")
        void context_defensiveCopyShouldNotAffectOriginal() {
            // Arrange
            TransactionResult result = TransactionResult.success(testContext);

            // Act
            TransactionContext returnedContext = result.context();
            returnedContext.put("modified_key", "modified_value");
            returnedContext.addExecutedStep("modified_step");

            // Assert - Original context should not be affected
            assertNull(
                    testContext.get("modified_key"),
                    "Original context should not contain modified key");
            assertEquals(
                    1,
                    testContext.getExecutedSteps().size(),
                    "Original context should have original step count");
            assertEquals(
                    "step1",
                    testContext.getExecutedSteps().get(0),
                    "Original context should have original step");

            // Assert - Returned context should have modifications
            assertEquals("modified_value", returnedContext.get("modified_key"));
            assertEquals(2, returnedContext.getExecutedSteps().size());
            assertTrue(returnedContext.getExecutedSteps().contains("modified_step"));
        }

        @Test
        @DisplayName("context() should preserve complex context data")
        void context_shouldPreserveComplexContextData() {
            // Arrange
            testContext.put("string_value", "test_string");
            testContext.put("integer_value", 42);
            testContext.put("boolean_value", true);
            testContext.addExecutedStep("step2");
            testContext.addExecutedStep("step3");

            TransactionResult result = TransactionResult.failure(testException, testContext);

            // Act
            TransactionContext returnedContext = result.context();

            // Assert
            assertEquals("test_string", returnedContext.get("string_value"));
            assertEquals(42, returnedContext.<Integer>get("integer_value"));
            assertEquals(true, returnedContext.<Boolean>get("boolean_value"));
            assertEquals(3, returnedContext.getExecutedSteps().size());
            assertTrue(returnedContext.getExecutedSteps().contains("step1"));
            assertTrue(returnedContext.getExecutedSteps().contains("step2"));
            assertTrue(returnedContext.getExecutedSteps().contains("step3"));
        }
    }

    @Nested
    @DisplayName("Record Properties")
    class RecordPropertiesTest {

        @Test
        @DisplayName("success property should reflect the success state")
        void success_shouldReflectSuccessState() {
            // Arrange & Act
            TransactionResult successResult = TransactionResult.success(testContext);
            TransactionResult failureResult = TransactionResult.failure(testException, testContext);

            // Assert
            assertTrue(successResult.success());
            assertFalse(failureResult.success());
        }

        @Test
        @DisplayName("Record should implement equals and hashCode correctly")
        void record_shouldImplementEqualsAndHashCodeCorrectly() {
            // Arrange
            TransactionResult result1 = TransactionResult.success(testContext);
            TransactionResult result2 = TransactionResult.success(testContext);
            TransactionResult result3 = TransactionResult.failure(testException, testContext);

            // Assert
            assertEquals(result1, result2, "Results with same values should be equal");
            assertEquals(result1.hashCode(), result2.hashCode(), "Hash codes should be equal");
            assertNotEquals(result1, result3, "Results with different values should not be equal");
        }

        @Test
        @DisplayName("Record should implement toString correctly")
        void record_shouldImplementToStringCorrectly() {
            // Arrange
            TransactionResult result = TransactionResult.success(testContext);

            // Act
            String toString = result.toString();

            // Assert
            assertNotNull(toString);
            assertTrue(toString.contains("TransactionResult"));
            assertTrue(toString.contains("success=true"));
        }
    }

    @Nested
    @DisplayName("Edge Cases")
    class EdgeCasesTest {

        @Test
        @DisplayName("Should handle different exception types")
        void shouldHandleDifferentExceptionTypes() {
            // Arrange
            RuntimeException runtimeException = new RuntimeException("Runtime exception");
            IllegalStateException illegalStateException =
                    new IllegalStateException("Illegal state");
            Exception checkedException = new Exception("Checked exception");

            // Act
            TransactionResult result1 = TransactionResult.failure(runtimeException, testContext);
            TransactionResult result2 =
                    TransactionResult.failure(illegalStateException, testContext);
            TransactionResult result3 = TransactionResult.failure(checkedException, testContext);

            // Assert
            assertTrue(result1.exception() instanceof RuntimeException);
            assertTrue(result2.exception() instanceof IllegalStateException);
            assertTrue(result3.exception() instanceof Exception);
            assertEquals("Runtime exception", result1.exception().getMessage());
            assertEquals("Illegal state", result2.exception().getMessage());
            assertEquals("Checked exception", result3.exception().getMessage());
        }

        @Test
        @DisplayName("Should handle empty context")
        void shouldHandleEmptyContext() {
            // Arrange
            TransactionContext emptyContext = new TransactionContext();

            // Act
            TransactionResult result = TransactionResult.success(emptyContext);

            // Assert
            assertTrue(result.success());
            assertNotNull(result.context());
            assertTrue(result.context().getExecutedSteps().isEmpty());
        }

        @Test
        @DisplayName("Multiple calls to accessors should return new defensive copies each time")
        void multipleCallsToAccessorsShouldReturnNewCopies() {
            // Arrange
            TransactionResult result = TransactionResult.failure(testException, testContext);

            // Act
            Exception exception1 = result.exception();
            Exception exception2 = result.exception();
            TransactionContext context1 = result.context();
            TransactionContext context2 = result.context();

            // Assert
            assertNotSame(
                    exception1, exception2, "Each call should return a new copy of exception");
            assertNotSame(context1, context2, "Each call should return a new copy of context");
            assertEquals(
                    exception1.getMessage(),
                    exception2.getMessage(),
                    "Exception copies should have same content");
            assertEquals(
                    context1.<String>get("test_key"),
                    context2.<String>get("test_key"),
                    "Context copies should have same content");
        }
    }
}
